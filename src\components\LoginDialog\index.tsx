import { Button, Image, Text, View } from '@tarojs/components'
import { useEffect, useMemo, useState } from 'react'

import styles from './index.module.scss'
import IconFont from '../IconFont'
import ModalBody from '@/components/Modal/Body'
import LoginProtocol from '../LoginProtocol'
import { goToAgreement } from '@/utils/login'
import { actions, dispatch, useSelector } from '@/core/store'
import useClick from '@/hooks/useClick'

function LoginDialog() {
  const [checked, $checked] = useState(false)

  const [pVisible, $pVisible] = useState(false)

  const currentPage = useMemo(() => $.taro.getCurrentPages().pop() || {}, [])

  const { pageId, success, fail } = useSelector((state) => state.global.showLoginModel)

  const visible = useMemo(() => pageId === currentPage.__wxWebviewId__, [pageId, currentPage])
  const onClose = () => dispatch(actions.global.setState({ showLoginModel: {} }))

  /** 每次弹出时刷新数据 */
  useEffect(() => {
    if (visible) {
      $.showLoading('加载中...')
      $checked(false)
      $pVisible(false)
    }
  }, [visible])

  /** 协议勾选样式 */
  const iconColor = useMemo(() => {
    if (checked) {
      return 'rgb(0, 146, 255)'
    }
    return 'rgba(0,0,0,.25)'
  }, [checked])
  /** 获取手机号登录
   * @param { boolean } certainProtocol 是否已阅读并同意协议
   * @returns {Function} eventHandler
   */
  const fakerLogin = useClick(async () => {
    if (pVisible) {
      $pVisible(false)
      $checked(true)
    }

    $.showLoading('登录中...')
    // const shareReq = undefined as unknown as ShareReq
    $.taro.login().then(result => console.log('XHS LOGIN RESULT', result)).finally(() => $.hideLoading())
    // TODO 登录失败 跳转到账号验证码登录
    toLoginPage()
  })
  const toLoginPage = useClick(async () => {
    $.router.push('/pages/auth-login/index', { back: true }, {}, (logged: boolean) => {
      if (logged) {
        onClose && onClose()
        success && success()
      } else {
        fail && fail()
      }
    })
  })

  // 这个地方处理弹窗渲染慢的情况，使用图片的load事件来关闭加载动画
  const onLoadImg = () => {
    $.hideLoading()
  }

  return (
    <>
      <ModalBody visible={visible} onMaskClick={() => {
        onClose()
        fail && fail()
      }}
      >
        <View className={styles.container}>
          <Image onError={onLoadImg} onLoad={onLoadImg} src="https://cdn.yupaowang.com/yupao_mini/alipay-mini-login-log.png" className={styles.logo}></Image>
          <Text className={styles.loginText}>登录鱼泡直聘</Text>
          {checked ? <Button onClick={fakerLogin}
            className={styles.loginBtn}
          >小红书账号登录</Button>
            : <Button className={styles.loginBtn} onClick={() => $pVisible(true)}>小红书账号登录</Button>}
          <Text className={styles.verifyCodeLogin} onClick={toLoginPage}>用其他手机号码登录</Text>
          <View className={styles.protocol} onClick={() => $checked(!checked)}>
            <View><IconFont className={styles.checkedIcon} color={iconColor} type="yp-alipay-checked-round"></IconFont></View>
            <Text>已阅读并同意
              <Text className={styles.primary} onClick={goToAgreement} data-type="privacy">《隐私政策》</Text>
              <Text className={styles.primary} onClick={goToAgreement} data-type="user">《服务协议》</Text>
            并授权鱼泡直聘
            </Text>
          </View>
        </View>
      </ModalBody>
      <LoginProtocol visible={pVisible} onClose={() => $pVisible(false)} fakerLogin={fakerLogin}></LoginProtocol>
    </>)
}

// function loginWithTelPermission(ticketData: any, shareReq: ShareReq, onClose: any, $pVisible, success?, fail?, jumpEvent?): void | PromiseLike<void> {
//   return $.taro.getPhoneNumber()
//     .then(response => response.response)
//     .then(response => JSON.parse(response))
//     .then(({ response, sign }) => $.request['POST/account/v1/sns/alipayBindTel'](delNullOp({
//       response,
//       ticket: ticketData.ticket,
//       sign,
//       shareReq,
//       appId: $.config.appid,
//     }), { hideMsg: true }))
//     .then(async ([data, res]) => {
//       $.hideLoading()
//       if (data.token) {
//         afterLogin(data.token)
//         $pVisible(false)
//         $.msg('登录成功')
//         success && success()
//         onClose && onClose()
//       } else {
//         $.msg(res.message)
//         fail && fail()
//       }
//     })
//     .catch(async ([_, error]) => {
//       $.hideLoading()
//       if (error && (error.code == ******** || error.code == ********)) {
//         fail && fail()
//         jumpEvent && jumpEvent()
//         await $.msg('无法获取支付宝绑定手机号,为您选择手机号验证码登录')
//         $.router.push('/pages/auth-login/index', { back: true }, {}, (logged: boolean) => {
//           if (logged) {
//             onClose && onClose()
//             success && success()
//           } else {
//             fail && fail()
//           }
//         })
//         return
//       }

//       const dialogIdentify = $.getObjVal(error, 'data.dialogData.dialogIdentify') || $.getObjVal(error, 'data.data.dialogData.dialogIdentify')
//       if (dialogIdentify === 'limitOperate' || (error && error.code == 10110009)) {
//         $.confirm({
//           title: '温馨提示',
//           content: '您的登录/注册环境异常，请稍后再试',
//           confirmText: '联系客服',
//           cancelText: '知道了',
//         }).then(() => {
//           $.taro.makePhoneCall({ phoneNumber: '4008381888' })
//         })
//         fail && fail()
//         return
//       }
//       // fail && fail()
//       $.msg('登录失败, 请重试')
//     })
// }

// export const useLogin = (): [LoginProps, () => Promise<unknown> ] => {
//   // const [visible, $visible] = useState(false)
//   const success = useRef<(...args: any) => void>()
//   const fail = useRef<(...args: any) => void>()

//   const currentPage = useMemo(() => $.taro.getCurrentPages().pop(), [])

//   const showLoginModel = useSelector((state) => state.global.showLoginModel)

//   console.log('---------', currentPage, showLoginModel)

//   const login = useCallback(async () => {
//     $loginVisible(true)

//     return new Promise<any>((resolve, reject) => {
//       success.current = () => {
//         resolve(undefined)
//         fail.current = undefined
//       }
//       fail.current = reject
//     })
//   }, [$loginVisible])

//   return [{ visible: showLoginPage === currentPage?.$id, onClose: () => { $loginVisible(false) }, success: success.current, fail: () => { fail.current && fail.current(); $loginVisible(false) } }, login]
// }

export default LoginDialog
