/* components/ImagePreview/index.scss */
.preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;

  .preview-swiper {
    height: 100%;


    .preview-swiper-item {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .preview-image {
      width: 100%;
    }
  }

  // 底部指示器样式
  .preview-indicator {
    position: absolute;
    right: 44rpx;
    bottom: 56rpx;
    z-index: 1001;
    color: #fff;
    font-size: 30rpx;
  }
}

// 新增关闭按钮样式
.preview-close {
  position: absolute;
  left: 14rpx;
  top: 222rpx;
  z-index: 1001;
  width: 40rpx;
  height: 40rpx;
  border-radius: 999px;
  box-sizing: content-box;
  padding: 8rpx;
  background-color: rgba(0,0,0,0.2);
}
