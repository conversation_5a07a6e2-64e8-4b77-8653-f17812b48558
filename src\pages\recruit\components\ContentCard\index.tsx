import { View, Text } from '@tarojs/components'
import { useCallback, useEffect, useMemo, useState } from 'react'
import classNames from 'classnames'
import styles from './index.module.scss'
import { Card } from '../Card'
import IconFont from '@/components/IconFont'

export default (props: any) => {
  const { info = {}, resTags = [], loading } = props

  const [compact, $compact] = useState<any[]>([])

  const [diffed, $diffed] = useState(false)

  const [showViewAll, $showViewAll] = useState(false)

  const [viewAllText, $viewAllText] = useState(false)

  const [showContent, $showContent] = useState(false)

  useMemo(() => {
    $compact(resTags)
  }, [resTags.length])

  const expensiveFn = useCallback(async () => {
    const observer = $.taro.createIntersectionObserver({ selectAll: true, thresholds: [0, 1], dataset: true })
    observer.relativeTo('#compact-grid', {})
    try {
      //  切换成 this.createIntersectionObserver 减少观察范围
      const map = new Map()
      /** 创建定时器，如果50ms内没有变化，则认为已经获取完成所有节点 */
      let timer
      await new Promise((resolve) => {
        observer.observe(`.${styles.gridItem}`, (payload) => {
          map.set(payload.id, payload)
          clearTimeout(timer)
          timer = setTimeout(resolve, 50)
        })
      })

      /** ID(-1) 获取`展开全部`的宽度 */
      const expandAllWidth = Math.ceil(map.get('-1').boundingClientRect.width)
      /** 根据ID从小到大排序（此处ID来自于`compactList`的`mapIndex`） */
      const lastIndex = Array.from(map.entries()).map(([k]) => Number(k)).sort((a, b) => a - b).pop()!

      /** 如果能正确渲染出所有元素则不需要继续计算， */
      if (lastIndex >= compact.length - 1) {
        $diffed(true)
        return
      }
      /** 获取可视区域最后一个元素的宽度 */
      const lastItemWidth = Math.ceil(map.get(String(lastIndex)).boundingClientRect.width)
      /** 如果最后一个元素宽度小于`展开全部`的宽度，则直接替换展示，否则需要额外减去一个元素以保证`展开全部`能正常展示出来 */
      if (expandAllWidth < lastItemWidth) {
        $compact(compact.slice(0, lastIndex))
      } else {
        $compact(compact.slice(0, lastIndex - 1))
      }
      $diffed(true)
      observer.disconnect()
    } catch (error) {
      observer.disconnect()
      console.error(error)
    }
  }, [compact.length])

  const showAllTags = () => {
    $compact(resTags)
  }

  /** 是否展示投诉按钮 */
  // const showComplaint = useMemo(() => {
  //   const { token, userInfo } = store.getState().storage
  //   const isMine = !!token && userInfo && userInfo.userId == info.userId
  //   const isLook = $.getObjVal(info, 'aboutInfoSpecifics.isLook')
  //   const isComplaint = $.getObjVal(info, 'aboutInfoSpecifics.isComplaint')
  //   return !isMine && isLook && !isComplaint
  // }, [info])

  /** 计算内容高度，以渲染查看全部按钮 */
  const contentFn = useCallback(async () => {
    const selector = $.taro.createSelectorQuery()
    const rectHeigh = await new Promise<number>((resolve) => selector.select('#content').boundingClientRect().exec(([res, ...ext]) => {
      res && resolve(res.height)
    }))
    const btnHeight = await new Promise<number>((resolve) => selector.select('#viewItem').boundingClientRect().exec((res) => {
      if (res) {
        // eslint-disable-next-line sonarjs/no-nested-functions
        const viewAll = res.find((item) => item && item.id == 'viewItem')
        resolve((viewAll.height * 15))
      }
    }))

    $showContent(true)
    if (rectHeigh > btnHeight) {
      $showViewAll(true)
    }
  }, [])

  const handleViewAllText = () => {
    if (showViewAll) {
      $viewAllText(true)
    }
  }

  useEffect(() => {
    if (info.detail) {
      contentFn()
    }
  }, [info.detail])

  return (
    <Card>
      <View className={classNames(styles.container, { [styles.hidden]: !info.detail })}>
        <View className={styles.cardTitle}>
          <View className={styles.title}>职位详情</View>
          {/* <View className={classNames(styles.extra, { [styles.hidden]: showComplaint })}><IconFont type="yp-dtl_ic_warning" size={32} className={styles.complain}></IconFont>投诉</View> */}
        </View>
        <View className={classNames(styles.compact, { [styles.hidden]: loading || !compact.length })} style={!diffed || compact.length < resTags.length ? {} : { maxHeight: 'none' }} id="compact-grid" onFirstAppear={expensiveFn}>
          {compact.map((item, key) => <View key={key} className={styles.gridItem} id={`${key}`} style={{ opacity: diffed ? 1 : 0 }}>{item.name}</View>)}
          <View className={styles.gridItem} id="-1" style={{ opacity: 0, position: 'absolute', left: 0, top: 0 }}><Text>查看全部</Text><IconFont type="yp-dtl_ic_expand_all" className={styles.arrowDown} size={8}></IconFont></View>
          <View className={styles.gridItem} id="-2" style={{ display: compact.length < resTags.length ? 'flex' : 'none' }} onClick={showAllTags}><Text>查看全部</Text><IconFont type="yp-dtl_ic_expand_all" className={styles.arrowDown} size={8}></IconFont></View>
        </View>
        <View className={classNames(styles.content, { [styles.showContent]: showContent })} id="content" style={viewAllText ? { maxHeight: 'none' } : {}}>
          {info.detail || ''}
          <View id="viewItem" style="position: absolute; opacity: 0;">高度计算</View>
          <View className={styles.viewAll} style={{ opacity: showViewAll && !viewAllText ? 1 : 0 }} id="viewAll" onClick={handleViewAllText}>查看全部</View>
        </View>
      </View>
    </Card>
  )
}
