import { View, Image, Swiper, SwiperItem } from '@tarojs/components'
import './index.scss'
import { useState } from 'react'

interface IProps {
  images: string[] // 支持网络图片URL数组
  currentIndex?: number
  onClose: () => void
}

const ImagePreview: React.FC<IProps> = ({ images, currentIndex = 0, onClose }) => {
  const [current, setCurrent] = useState(currentIndex)

  // 处理滑动切换
  const handleSwiperChange = (e) => {
    setCurrent(e.detail.current)
  }

  return (
    <View className="preview-mask" onClick={onClose}>
      {/* 新增关闭按钮 */}
      <Image
        src="https://cdn.yupaowang.com/yupao_mini/Close-small-white.svg"
        className="preview-close"
        mode="widthFix"
        onClick={(e) => {
          e.stopPropagation()
          onClose()
        }}
      />

      {/* 图片展示区域 */}
      <Swiper
        className="preview-swiper"
        current={current}
        onChange={handleSwiperChange}
      >
        {images?.map((img, index) => (

          <SwiperItem key={index} className="preview-swiper-item">
            <Image
              src={img}
              mode="widthFix"
              className="preview-image"
              onClick={(e) => e.stopPropagation()}
            />
          </SwiperItem>
        ))}
      </Swiper>

      {/* 底部指示器 */}
      <View className="preview-indicator">
        {current + 1}/{images.length}
      </View>
    </View>
  )
}

export default ImagePreview
