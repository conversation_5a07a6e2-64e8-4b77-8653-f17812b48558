import { View, Image } from '@tarojs/components'
import classNames from 'classnames'
import { useMemo } from 'react'
import { Card } from '../Card'
import styles from './index.module.scss'

export const HeaderCard = (props) => {
  const { info = {}, headTags = [] } = props

  const salary = useMemo(() => {
    let targetSalary = ''
    if (info.showPerfects) {
      const { occIds = [] } = $.router.query
      const salaryList = info.showPerfects.filter(item => item.salary)
      const explicitlySalaryList = salaryList.filter(item => item.salary !== '面议')
      /** 非推荐时, 取选中工种的薪资 */
      const expectedTarget = occIds.length ? salaryList.filter(item => occIds.includes(item.occId)) : []
      targetSalary = expectedTarget[0] ? expectedTarget[0].salary : ''
      /** 其他情况，优先取非面议薪资 */
      targetSalary = targetSalary || (explicitlySalaryList.length && explicitlySalaryList[0].salary) || (salaryList.length && salaryList[0].salary) || ''
    }
    return targetSalary
  }, [info])

  const address = info.urbanAreas
    ? [info.urbanAreas.cityName, info.urbanAreas.countyName].filter(item => item).join('·')
    : ''
  // const settlement = (info.showPerfects ? info.showPerfects.filter(item => item.settlementMethod) : [])[0]?.settlementMethod || ''

  // const renderTags = useCallback(() => {
  //   return headTags.map((item) => {
  //     switch (item.type) {
  //     case 15: {
  //       return <View className={styles.exp} id={item.type}>
  //         <Image className={styles.summaryIc} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/group.png"></Image>
  //         <View>{item.name}</View>
  //       </View>
  //     }
  //     }
  //   })
  // }, [headTags])

  return <Card className={styles.card}>
    <View className={styles.container}>
      <View className={styles.title}>{info.title}</View>
      <View className={styles.salary}>{salary}</View>
      <View className={styles.summary}>
        <View className={classNames(styles.location, { [styles.hidden]: !address })}>
          <Image className={styles.summaryIc} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/positioning.png"></Image>
          <View className={styles.summaryText}>{address}</View>
        </View>
        { headTags.map(item => <View className={styles.settlement} key={item.type}>
          <Image className={classNames(styles.summaryIc, { [styles.hidden]: item.type !== 6 })} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/salary.png"></Image>
          <Image className={classNames(styles.summaryIc, { [styles.hidden]: item.type !== 15 })} src="https://cdn.yupaowang.com/yupao_mini/work_exp.png"></Image>
          <Image className={classNames(styles.summaryIc, { [styles.hidden]: item.type !== 16 })} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/group.png"></Image>
          <Image className={classNames(styles.summaryIc, { [styles.hidden]: item.type !== 17 })} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/construction_period.png"></Image>
          <View className={styles.summaryText}>{item.name}</View>
        </View>) }
        {/* <View className={styles.exp}>
          <Image className={styles.summaryIc} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/group.png"></Image>
          <View>全职</View>
        </View>
        <View className={classNames(styles.settlement, { [styles.hidden]: !settlement })}>
          <Image className={styles.summaryIc} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/salary.png"></Image>
          <View>{settlement}</View>
        </View> */}
        {/* <View className={styles.edu}>
          <Image className={styles.summaryIc} src="https://cdn.yupaowang.com/yp_mini/images/ahaya/construction_period.png"></Image>
          <View>学历不限</View>
        </View> */}
      </View>
    </View>
  </Card>
}
