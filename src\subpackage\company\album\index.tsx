import { useEffect, useCallback, useRef } from 'react'
import { ScrollView, View as V, Image as Img } from '@tarojs/components'
import Tab from '@/components/Tab'
import useReducer from '@/hooks/useReducer'
import styles from './index.module.scss'
// import Header from '@/components/Header'
import ImagePreview from '@/components/imagesPreview'

type Res = Models['POST/enterprise/v1/enterpriseHomepage/enterpriseMainView']['Res']['data']

type InitState = {
  videoList: Res['enterpriseVideoList']
  imageList: Res['enterpriseAlbumList']
  items: string[]
  intoView?: 'videoDom' | 'photoDom'
  tagChoose?: 0 | 1
  showPreview: boolean
  currentIndex: number
  isManualChange?: boolean // 新增手动切换标志位
}

/**
 * 相册页面
*/
export default () => {
  const { infoId } = $.router.query || {}

  const [{
    items,
    videoList,
    imageList,
    intoView,
    tagChoose,
    showPreview,
    currentIndex,
    isManualChange, // 新增标志位解构
  }, dispatch] = useReducer<InitState>({
    videoList: [],
    imageList: [],
    items: [],
    tagChoose: 0,
    showPreview: false,
    currentIndex: 0,
    isManualChange: false, // 初始化标志位
  })

  const disTop = useRef(0)

  /**
   * 请求，获取数据
  */
  const getData = useCallback(async () => {
    try {
      const [data] = await $.request['POST/enterprise/v1/enterpriseHomepage/enterpriseMainView']({
        enterpriseBaseInfoId: infoId ? +infoId : undefined,
        tenantKey: 'YPZP',
        // wechat_token: "wxdcc7f8e70a22d31f"
      })
      const { enterpriseAlbumList, enterpriseVideoList } = data
      const i = [] as string[]
      enterpriseVideoList.length > 0 && i.push('公司视频')
      enterpriseAlbumList.length > 0 && i.push('公司照片')
      dispatch({
        videoList: enterpriseVideoList || [],
        imageList: enterpriseAlbumList || [],
        items: i,
      })
    } catch (error) {
      console.log(error)
    }
  }, [])

  const tabChange = (i: 0 | 1) => {
    dispatch({
      intoView: ['videoDom', 'photoDom'][i] as InitState['intoView'],
      tagChoose: i,
      isManualChange: true, // 标记为手动切换
    })
    // 滚动动画完成后重置标志位（根据实际动画时长调整500ms）
    setTimeout(() => dispatch({ isManualChange: false }), 500)
  }

  const getImageDom = () => {
    const SelectorQuery = $.taro.createSelectorQuery()
    SelectorQuery.select('#photoDom')
      .boundingClientRect()
      .exec(([res]) => {
        if (!res) {
          return
        }

        disTop.current = (res.top - res.height)
      })
  }

  const previewVideo = (url: string) => {
    $.router.push('/subpackage/company/video-preview/index', { url })
  }

  const previewImage = (index: number) => {
    if ($.isIos()) {
      dispatch({ showPreview: true, currentIndex: index })
    } else {
      const urls = imageList.map(it => it.url)
      // 快手小程序使用原生 ks.previewImage API
      if (process.env.TARO_ENV === 'kwai') {
        ks.previewImage({ urls, current: urls[index] })
      } else {
        // 其他平台使用 taro 的 previewImage API
        $.taro.previewImage({ urls, current: index })
      }
    }
  }

  const onScroll = useCallback(
    $.throttle((e) => {
      if (isManualChange) return // 手动切换期间跳过滚动逻辑

      if (items.length < 2) return
      const { scrollTop } = e.detail

      tagChoose === 0 && scrollTop > disTop.current && dispatch({ tagChoose: 1 })
      tagChoose === 1 && scrollTop <= disTop.current && dispatch({ tagChoose: 0 })
    }, 200),
    [items, tagChoose, disTop, isManualChange], // 新增标志位到依赖数组
  )

  useEffect(() => {
    getData()
  }, [])

  useEffect(() => {
    if (items.length > 1) {
      getImageDom()
    }
  }, [items])

  return (
    <>
      {showPreview && (
        <ImagePreview
          images={imageList?.map(it => it.url)}
          currentIndex={currentIndex}
          onClose={() => {
            dispatch({ showPreview: false, currentIndex: 0 })
          }}
        />
      )}
      {/* <Header bottom={!!items.length && <Tab items={items} onChange={tabChange} value={tagChoose} />} /> */}
      <Tab items={items} onChange={tabChange} value={tagChoose} />
      <V>
        <ScrollView scrollY className={styles.scrollView} scrollIntoView={intoView} onScroll={onScroll} style={{ paddingBottom: $.sysInfo().bottomSafeAreaHeight }}>
          {
            !!videoList.length && (
              <>
                <V className={styles.title} id='videoDom'>公司视频</V>
                <V className={styles.list}>
                  {
                    videoList.map((item) => (
                      <V className={styles.box} key={item.id} onClick={() => previewVideo(item.url)}>
                        <Img className={styles.box} src={item.cover} mode='aspectFill'></Img>
                        <Img className={styles.icon} src="https://staticscdn.zgzpsjz.com/miniprogram/images/ygd/yp-mini_icon-video-btn.png" mode="aspectFill" />
                      </V>
                    ))
                  }
                </V>
              </>
            )
          }
          {
            !!imageList.length && (
              <>
                <V className={styles.title} id='photoDom'>公司照片</V>
                <V className={styles.list}>
                  {
                    imageList.map((item, index) => (
                      <V className={styles.box} key={item.id} onClick={() => previewImage(index)}>
                        <Img className={styles.box} src={item.url} mode='aspectFill'></Img>
                      </V>
                    ))
                  }
                </V>
              </>
            )
          }
        </ScrollView>
      </V>
    </>
  )
}
