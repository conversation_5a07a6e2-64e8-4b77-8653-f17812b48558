.page {
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  overflow: hidden;
}

.scrollViewOut {
  flex: 1;
  display: flex;
}

.leftScrollView {
  width: 260rpx;
  height: 100%;
  border-right: 1rpx solid rgba(233, 237, 243, 1);
}

.rightScrollView {
  flex: 1;
}

.btmHt {
  width: 100%;
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 IOS<11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 IOS>11.2 */
}

.fotter {
  position: fixed;
  width: 100vw;
  bottom: 0px;
  left: 0px;
}
