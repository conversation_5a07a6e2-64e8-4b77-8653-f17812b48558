/*
 * @Date: 2025-03-08 09:33:28
 * @Description: 卡片曝光
 */

import { useRef, useEffect } from 'react'
import { useDidHide, useDidShow } from '@tarojs/taro'

/** 曝光时长 2 秒 */
const exposureDuration = 2000
const intersectionRatio = 0.31

export default function useCardReport(classId, getHeight, callback) {
  const itemExposureDuration = useRef({
    report: false,
    now: 0,
    hideTime: 0,
    showTime: 0,
    visible: false,
  })

  const onHideReport = () => {
    if (itemExposureDuration.current.showTime) {
      // 隐藏的时间
      const hideTime = itemExposureDuration.current.showTime - itemExposureDuration.current.hideTime
      if (!itemExposureDuration.current.report && itemExposureDuration.current.now && Date.now() - itemExposureDuration.current.now - hideTime > exposureDuration) {
        itemExposureDuration.current.report = true
        callback(Date.now() - itemExposureDuration.current.now - hideTime)
      } else if (!itemExposureDuration.current.report) {
        itemExposureDuration.current.now = 0
      }
    } else if (!itemExposureDuration.current.report && itemExposureDuration.current.now && Date.now() - itemExposureDuration.current.now > exposureDuration) {
      itemExposureDuration.current.report = true
      callback(Date.now() - itemExposureDuration.current.now)
    } else if (!itemExposureDuration.current.report) {
      itemExposureDuration.current.now = 0
    }
  }

  // 列表曝光埋点
  useEffect(() => {
    let observer
    Promise.all(getHeight()).then(([height, bottom]) => {
      // 创建 IntersectionObserver 实例
      observer = $.taro.createIntersectionObserver(null as any, {
        thresholds: [0, intersectionRatio], // 触发回调的交叉比例
        initialRatio: 0, // 初始交叉比例
      })
      // 设置参考区域为视口，并监听目标元素
      observer.relativeToViewport({ top: -height, bottom: -bottom }).observe(`.${classId}`, (res) => {
        if (!itemExposureDuration.current.visible && !itemExposureDuration.current.report && !itemExposureDuration.current.now) {
          itemExposureDuration.current.now = Date.now()
          itemExposureDuration.current.visible = true
        } else {
          itemExposureDuration.current.visible = false
          // 超过2秒进行上报 离开视图
          onHideReport()
        }
      })
    })
    // 清理函数，在组件卸载时停止监听
    return () => {
      onHideReport()
      observer && observer.disconnect()
    }
  }, [])

  useDidHide(() => {
    itemExposureDuration.current.hideTime = Date.now()
    onHideReport()
  })

  useDidShow(() => {
    itemExposureDuration.current.showTime = Date.now()
  })
}
