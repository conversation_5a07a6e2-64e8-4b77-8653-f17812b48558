import { dispatch, actions, getState, store, useSelector } from '@/core/store'
import type { ITreeArea } from './type'
import { saveAreaStorage, getAreaStorage } from './utils'
import { MAP_KEY } from '@/core/config'

/** 数据字段转换 */
function handlerArea(area, level = 1): ILocation.TAreaData {
  return {
    id: +area.id,
    ad_code: area.adCode || area.ad_code,
    pid: +area.pid,
    level,
    letter: area.letter,
    name: area.name,
    ad_name: area.adName,
  }
}

/** 获取地区数据 */
async function fetchAreaData(): Promise<Models['POST/lbs/v2/area/tree']['Res']['data']['data'][number]> {
  const [data] = await $.request['POST/lbs/v2/area/tree']()
  if (data && $.isArrayVal(data.data)) {
    return data.data[0]
  }
  return {
    adCode: '100000',
    adName: '中华人民共和国',
    areaStatus: 1,
    areaType: 0,
    cityCode: '',
    id: 1,
    letter: 'quanguo',
    name: '全国',
    pid: 0,
    subAreaList: [],
  }
}

/** 获取地区树 */
export async function getTreeData(isUpdate = false) {
  const areaTree = getAreaStorage()
  let treeData: ILocation.TAreaData[] = []
  if ($.isArrayVal(areaTree, 30) && !isUpdate) {
    /** 存储map到model */
    dispatch(actions.address.setAreaMap(areaTree))
    return $.deepClone(areaTree) as ILocation.TAreaData[]
  }

  const { subAreaList, ...quanguo } = await fetchAreaData()
  treeData = [
    handlerArea(quanguo),
  ]

  function recurve(area, level = 1) {
    let areaObj: any = {}
    const { subAreaList, ...item } = area
    areaObj = handlerArea(item, level)
    if ($.isArrayVal(subAreaList)) {
      const children = subAreaList.map(item => recurve(item, level + 1))
      areaObj.children = children
    }
    return areaObj
  }

  subAreaList.forEach(item => {
    treeData.push(recurve(item, 1))
  })
  /** 保存地区数据 */
  saveAreaStorage(treeData)
  /** 存储map到model */
  dispatch(actions.address.setAreaMap(treeData))
  return $.deepClone(treeData)
}

/** 根据地区id，返回省市区
 * @description 注意:id为省的id只返回省的信息,id为市的id只返回省和市的信息，id为区的id则返回省市区信息
 * @param areaId string|number
 * @returns { province: '', city: '', district: '', current: '' }
 * */
export const getAreaById = async (areaId: string | number): Promise<ITreeArea> => {
  return getAreaSearch(areaId, 'id')
}

/** 根据地区adcode，返回省市区
 * @description 注意:id为省的id只返回省的信息,id为市的id只返回省和市的信息，id为区的id则返回省市区信息
 * @param adcode string|number
 * @returns { province: '', city: '', district: '', current: '' }
 * */
export const getAreaByAdcode = async (adcode: string | number): Promise<ITreeArea> => {
  return getAreaSearch(adcode, 'ad_code')
}

/**
 * @description 根据地址信息，返回省市区
 * @param areaVal string|number 地址id或者地址的编码
 * @param isDelChildren boolean 是否去掉省市区的children-默认不去掉
 * @returns { province: '', city: '', district: '', current: '' }
 * */
export async function getAreaSearch(
  areaVal: string | number,
  type: 'ad_code' | 'id' | null = null,
): Promise<ITreeArea> {
  const areInfo = { province: '', city: '', district: '', current: '' } as ITreeArea

  if (!areaVal) {
    return areInfo
  }

  let { areaAdCodeMap, areaIdMap } = getState().address
  if (!$.isObjVal(areaAdCodeMap, 10) || !$.isObjVal(areaIdMap, 10)) {
    await getTreeData()
    areaAdCodeMap = getState().address.areaAdCodeMap || {}
    areaIdMap = getState().address.areaIdMap || {}
  }

  function setAreaInfo(areaData: ITreeArea['current']) {
    if (!areaData) {
      return
    }
    if (areaData.level == 1) {
      areInfo.province = { ...areaData }
      return
    }
    if (areaData.level == 2) {
      areInfo.city = { ...areaData }
      const area = areaIdMap[areaData.pid] || ''
      if (!area) {
        return
      }
      setAreaInfo(area)
    }
    if (areaData.level == 3) {
      areInfo.district = { ...areaData, gid: '' }
      const area = areaIdMap[areaData.pid] || ''
      if (!area) {
        return
      }
      setAreaInfo(area)
    }
  }

  let current
  if (type === 'ad_code') {
    current = areaAdCodeMap[areaVal] || ''
  } else if (type === 'id') {
    current = areaIdMap[areaVal] || ''
  } else {
    current = areaAdCodeMap[areaVal] || areaIdMap[areaVal] || ''
  }
  if (current) {
    areInfo.current = { ...current }
    setAreaInfo(areInfo.current)
  }

  /** 给district加上gid */
  if (areInfo.district && areInfo.current) {
    areInfo.district.gid = areInfo.province.id
    areInfo.current.gid = areInfo.province.id
  }

  return areInfo
}

/**
 * @description 获取地区信息
 * @param {Array<{string | number}>} areaVals 地区id或者ad_code
 * @param {string} type 地区类型，默认为ad_code
 * @return {Promise<ITreeArea[]>}
 */
export async function getAreaSearches(
  areaVals: (string | number)[],
  type: 'ad_code' | 'id' | null = null,
) {
  if ($.isArrayVal(areaVals)) {
    const areaInfos = areaVals.map(areaVal => {
      return getAreaSearch(areaVal, type)
    })
    return Promise.all(areaInfos)
  }
  return []
}

async function openSetLoca() {
  const setting = await $.taro.getSetting()

  if (setting && setting.authSetting) {
    const auth = setting.authSetting as any
    if (auth.location === false || auth['scope.userLocation'] === false) {
      const res = await $.taro.openSetting()
      if (res && res.authSetting) {
        return auth.location === true || auth['scope.userLocation'] === true
      }
      return false
    }
  }
  return true
}

export function getLocation(): Promise<any> {
  return new Promise(async (resolve, reject) => {
    $.taro.getLocation({
      type: 'gcj02', // 代表获取经纬度
      success: async (res) => {
        const { province, city, district, current } = await getAreaByAdcode(res.districtAdcode || res.cityAdCode)
        const myCurrent = current || {}
        const value = {
          success: true,
          province,
          city,
          district,
          ...myCurrent,
          longitude: res.longitude,
          latitude: res.latitude,
        }
        dispatch(actions.storage.setItem({ key: 'userLocation', value }))
        resolve($.deepClone(value))
      },
      fail: async (err) => {
        if (err.error == 2001 || err.error == 2003) {
          reject({ success: false, code: 401, message: '', err })
          return
        }
        reject({
          success: false,
          err,
        })
      },
    } as any)
  })
}

/** 获取经纬度 */
export function haversineDistance(lat1, lon1, lat2, lon2) {
  if (!lat1 || !lon1 || !lat2 || !lon2) return ''
  const toRad = (value) => (value * Math.PI) / 180 // 将角度转换为弧度
  const earthRadius = 6371 // 地球半径（单位：公里）
  const dLat = toRad(lat2 - lat1)
  const dLon = toRad(lon2 - lon1)
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
            + Math.cos(toRad(lat1)) * Math.cos(toRad(lat2))
            * Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  const distanceNumber = c * earthRadius
  if (distanceNumber * 1000 < 1) return ''
  if (distanceNumber * 1000 < 1000) return `${(distanceNumber * 1000).toFixed(0)}m`
  return `${(distanceNumber).toFixed(2)}km` // 返回距离（单位：公里）
}

/**
 * 数组去重：可以是字符串或数字的数组，也可以是对象数组
 * @param array 需要去重的数组
 * @param key 如果是对象数组，需要指定用于去重的 key
 */
export const uniqueArray = (array: any[], key?: string) => {
  if (!Array.isArray(array)) return []

  if (key) {
    const seen = new Set()
    return array.filter((item) => {
      const val = item?.[key]
      if (val === undefined || seen.has(val)) return false
      seen.add(val)
      return true
    })
  }
  return Array.from(new Set(array))
}

/**
 * 根据经纬度，获取地理位置信息
 * @param longitude 经度 字符串，
 * @param latitude 纬度 字符串，
 * @returns {Promise<{addr: AddressComponen}>} 地址的信息
 */
export function getLocationByApi(longitude: string, latitude: string): Promise<{ addr: AddressComponent }> {
  return new Promise((resolve, reject) => {
    const location = `${longitude},${latitude}`
    $.taro.request({
      url: 'https://restapi.amap.com/v3/geocode/regeo',
      data: { key: MAP_KEY, location, extensions: 'all', platform: 'WXJS', s: 'rsx' },
      method: 'GET',
      header: { 'content-type': 'application/json' },
      success(res) {
        if (res.statusCode === 200 && res.data.status == '1') {
          resolve({
            addr: res.data.regeocode.addressComponent,
          })
        } else {
          reject({})
        }
      },
      fail(err) {
        reject(err)
      },
    })
  })
}
