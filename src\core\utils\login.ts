import { actions, dispatch } from '../store'

/**
 * @description 全局的登录状态
 * @returns Promise<undefined>
 */
export const login = () => {
  return new Promise((resolve, reject) => {
    const currentPage = $.taro.getCurrentPages().pop() || {}
    dispatch(actions.global.setState({ showLoginModel: {
      pageId: currentPage.__wxWebviewId__,
      success: () => {
        resolve(undefined)
        dispatch(actions.global.setState({ showLoginModel: {} }))
      },
      fail: () => {
        reject()
        dispatch(actions.global.setState({ showLoginModel: {} }))
      },
    } }))
  })
}
